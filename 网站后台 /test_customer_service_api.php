<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP客服API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.8;
        }
        
        .test-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .section-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-form {
            display: grid;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-group label {
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-size: 14px;
        }
        
        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }
        
        .btn-deepseek {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }
        
        .btn-doubao {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        
        .result-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 100px;
        }
        
        .result-title {
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .result-content {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #10b981;
        }
        
        .status-error {
            background: #ef4444;
        }
        
        .status-loading {
            background: #f59e0b;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .api-docs {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .api-docs h2 {
            margin-bottom: 20px;
        }
        
        .api-endpoint {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .endpoint-url {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.2);
            padding: 8px 12px;
            border-radius: 4px;
            margin: 8px 0;
        }
        
        @media (max-width: 768px) {
            .test-sections {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 APP客服API测试</h1>
            <p>测试DeepSeek和豆包AI接口的完整功能</p>
        </div>
        
        <div class="test-sections">
            <!-- DeepSeek测试区域 -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-brain"></i>
                    DeepSeek API测试
                </h2>
                
                <form class="test-form" id="deepseek-form">
                    <div class="form-group">
                        <label>测试类型</label>
                        <select id="deepseek-action">
                            <option value="chat">聊天测试</option>
                            <option value="models">获取模型列表</option>
                            <option value="config">获取配置</option>
                            <option value="stats">获取统计</option>
                            <option value="test">连接测试</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="deepseek-message-group">
                        <label>测试消息</label>
                        <textarea id="deepseek-message" rows="3" placeholder="输入测试消息...">你好，请介绍一下你自己。</textarea>
                    </div>
                    
                    <div class="form-group" id="deepseek-model-group">
                        <label>选择模型</label>
                        <select id="deepseek-model">
                            <option value="">默认模型</option>
                            <option value="deepseek-chat">deepseek-chat</option>
                            <option value="deepseek-coder">deepseek-coder</option>
                            <option value="deepseek-reasoner">deepseek-reasoner</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-deepseek">
                        <span class="status-indicator" id="deepseek-status"></span>
                        发送测试请求
                    </button>
                </form>
                
                <div class="result-area">
                    <div class="result-title">测试结果：</div>
                    <div class="result-content" id="deepseek-result">等待测试...</div>
                </div>
            </div>
            
            <!-- 豆包测试区域 -->
            <div class="test-section">
                <h2 class="section-title">
                    <i class="fas fa-robot"></i>
                    豆包API测试
                </h2>
                
                <form class="test-form" id="doubao-form">
                    <div class="form-group">
                        <label>测试类型</label>
                        <select id="doubao-action">
                            <option value="chat">聊天测试</option>
                            <option value="models">获取模型列表</option>
                            <option value="config">获取配置</option>
                            <option value="stats">获取统计</option>
                            <option value="test">连接测试</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="doubao-message-group">
                        <label>测试消息</label>
                        <textarea id="doubao-message" rows="3" placeholder="输入测试消息...">你好，请介绍一下你自己。</textarea>
                    </div>
                    
                    <div class="form-group" id="doubao-model-group">
                        <label>选择模型</label>
                        <select id="doubao-model">
                            <option value="">默认模型</option>
                            <option value="doubao-lite-4k">豆包-lite-4k</option>
                            <option value="doubao-pro-4k">豆包-pro-4k</option>
                            <option value="doubao-pro-32k">豆包-pro-32k</option>
                            <option value="doubao-pro-128k">豆包-pro-128k</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-doubao">
                        <span class="status-indicator" id="doubao-status"></span>
                        发送测试请求
                    </button>
                </form>
                
                <div class="result-area">
                    <div class="result-title">测试结果：</div>
                    <div class="result-content" id="doubao-result">等待测试...</div>
                </div>
            </div>
        </div>
        
        <!-- API文档 -->
        <div class="api-docs">
            <h2>📚 API接口文档</h2>
            
            <div class="api-endpoint">
                <h3>DeepSeek独立API</h3>
                <div class="endpoint-url">POST /api/deepseek_api.php</div>
                <p>支持的动作：chat, models, config, stats, test</p>
            </div>
            
            <div class="api-endpoint">
                <h3>豆包独立API</h3>
                <div class="endpoint-url">POST /api/doubao_api.php</div>
                <p>支持的动作：chat, models, config, stats, test</p>
            </div>
            
            <div class="api-endpoint">
                <h3>统一客服API</h3>
                <div class="endpoint-url">POST /api/customer_service_api.php</div>
                <p>支持DeepSeek和豆包的统一管理接口</p>
            </div>
        </div>
    </div>
    
    <script>
        // DeepSeek表单处理
        document.getElementById('deepseek-form').addEventListener('submit', function(e) {
            e.preventDefault();
            testAPI('deepseek');
        });
        
        // 豆包表单处理
        document.getElementById('doubao-form').addEventListener('submit', function(e) {
            e.preventDefault();
            testAPI('doubao');
        });
        
        // 动作选择变化处理
        document.getElementById('deepseek-action').addEventListener('change', function() {
            toggleFormFields('deepseek', this.value);
        });
        
        document.getElementById('doubao-action').addEventListener('change', function() {
            toggleFormFields('doubao', this.value);
        });
        
        // 切换表单字段显示
        function toggleFormFields(provider, action) {
            const messageGroup = document.getElementById(provider + '-message-group');
            const modelGroup = document.getElementById(provider + '-model-group');
            
            if (action === 'chat') {
                messageGroup.style.display = 'flex';
                modelGroup.style.display = 'flex';
            } else {
                messageGroup.style.display = 'none';
                modelGroup.style.display = 'none';
            }
        }
        
        // 测试API
        function testAPI(provider) {
            const action = document.getElementById(provider + '-action').value;
            const message = document.getElementById(provider + '-message').value;
            const model = document.getElementById(provider + '-model').value;
            const statusIndicator = document.getElementById(provider + '-status');
            const resultDiv = document.getElementById(provider + '-result');
            
            // 设置加载状态
            statusIndicator.className = 'status-indicator status-loading';
            resultDiv.textContent = '正在发送请求...';
            
            // 构建请求数据
            const requestData = { action: action };
            
            if (action === 'chat') {
                requestData.message = message;
                if (model) {
                    requestData.model = model;
                }
            }
            
            // 发送请求
            fetch(`api/${provider}_api.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                statusIndicator.className = 'status-indicator ' + (data.success ? 'status-success' : 'status-error');
                resultDiv.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                statusIndicator.className = 'status-indicator status-error';
                resultDiv.textContent = '请求失败: ' + error.message;
            });
        }
        
        // 初始化表单字段显示
        toggleFormFields('deepseek', 'chat');
        toggleFormFields('doubao', 'chat');
    </script>
</body>
</html>
