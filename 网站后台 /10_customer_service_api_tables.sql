-- APP客服API功能数据库表结构
-- 版本: v1.0.0
-- 创建时间: 2024-12-19
-- 兼容性: MySQL 5.7.44+

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- 1. AI服务提供商配置表
CREATE TABLE IF NOT EXISTS `ai_providers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_name` varchar(50) NOT NULL COMMENT '服务提供商名称',
  `provider_key` varchar(50) NOT NULL COMMENT '服务提供商标识',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '描述',
  `base_url` varchar(255) DEFAULT NULL COMMENT '基础API URL',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_key` (`provider_key`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务提供商配置表';

-- 2. AI服务API配置表
CREATE TABLE IF NOT EXISTS `ai_api_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL COMMENT '服务提供商ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `api_key` text NOT NULL COMMENT 'API密钥(加密存储)',
  `base_url` varchar(255) DEFAULT NULL COMMENT 'API基础URL',
  `endpoint_id` varchar(100) DEFAULT NULL COMMENT '端点ID(豆包专用)',
  `timeout` int(11) DEFAULT '30' COMMENT '请求超时时间(秒)',
  `max_retries` int(11) DEFAULT '3' COMMENT '最大重试次数',
  `rate_limit` int(11) DEFAULT '100' COMMENT '速率限制(每分钟)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认配置',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider_id` (`provider_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_default` (`is_default`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_providers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI服务API配置表';

-- 3. AI模型配置表
CREATE TABLE IF NOT EXISTS `ai_model_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL COMMENT '服务提供商ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_id` varchar(100) NOT NULL COMMENT '模型ID',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `description` text COMMENT '模型描述',
  `max_tokens` int(11) DEFAULT '2048' COMMENT '最大Token数',
  `temperature` decimal(3,2) DEFAULT '0.70' COMMENT '温度值',
  `top_p` decimal(3,2) DEFAULT '0.90' COMMENT 'Top P值',
  `frequency_penalty` decimal(3,2) DEFAULT '0.00' COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT '0.00' COMMENT '存在惩罚',
  `system_prompt` text COMMENT '系统提示词',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认模型',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider_id` (`provider_id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_default` (`is_default`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_providers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型配置表';

-- 4. AI对话记录表
CREATE TABLE IF NOT EXISTS `ai_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conversation_id` varchar(64) NOT NULL COMMENT '对话ID',
  `provider_id` int(11) NOT NULL COMMENT '服务提供商ID',
  `model_id` int(11) NOT NULL COMMENT '模型ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `user_message` text NOT NULL COMMENT '用户消息',
  `ai_response` text COMMENT 'AI回复',
  `request_data` json COMMENT '请求数据',
  `response_data` json COMMENT '响应数据',
  `tokens_used` int(11) DEFAULT '0' COMMENT '使用的Token数',
  `response_time` int(11) DEFAULT '0' COMMENT '响应时间(毫秒)',
  `status` enum('pending','success','error','timeout') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `error_message` text COMMENT '错误信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_provider_id` (`provider_id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_providers` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`model_id`) REFERENCES `ai_model_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话记录表';

-- 5. AI使用统计表
CREATE TABLE IF NOT EXISTS `ai_usage_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL COMMENT '服务提供商ID',
  `model_id` int(11) DEFAULT NULL COMMENT '模型ID',
  `date` date NOT NULL COMMENT '统计日期',
  `total_requests` int(11) DEFAULT '0' COMMENT '总请求数',
  `successful_requests` int(11) DEFAULT '0' COMMENT '成功请求数',
  `failed_requests` int(11) DEFAULT '0' COMMENT '失败请求数',
  `total_tokens` int(11) DEFAULT '0' COMMENT '总Token使用量',
  `avg_response_time` int(11) DEFAULT '0' COMMENT '平均响应时间(毫秒)',
  `total_cost` decimal(10,4) DEFAULT '0.0000' COMMENT '总成本',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_provider_model_date` (`provider_id`, `model_id`, `date`),
  KEY `idx_provider_id` (`provider_id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_date` (`date`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_providers` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`model_id`) REFERENCES `ai_model_configs` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表';

-- 6. AI API密钥管理表
CREATE TABLE IF NOT EXISTS `ai_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL COMMENT '服务提供商ID',
  `key_name` varchar(100) NOT NULL COMMENT '密钥名称',
  `api_key` text NOT NULL COMMENT 'API密钥(加密存储)',
  `key_type` enum('primary','backup','test') NOT NULL DEFAULT 'primary' COMMENT '密钥类型',
  `usage_limit` int(11) DEFAULT NULL COMMENT '使用限制',
  `usage_count` int(11) DEFAULT '0' COMMENT '使用次数',
  `rate_limit` int(11) DEFAULT '100' COMMENT '速率限制(每分钟)',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider_id` (`provider_id`),
  KEY `idx_key_type` (`key_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`provider_id`) REFERENCES `ai_providers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI API密钥管理表';

-- 插入默认的AI服务提供商
INSERT INTO `ai_providers` (`provider_name`, `provider_key`, `display_name`, `description`, `base_url`, `is_active`, `sort_order`) VALUES
('DeepSeek', 'deepseek', 'DeepSeek AI', 'DeepSeek人工智能服务', 'https://api.deepseek.com', 1, 1),
('Doubao', 'doubao', '豆包AI', '字节跳动豆包AI服务', 'https://ark.cn-beijing.volces.com', 1, 2);

-- 插入默认的DeepSeek模型配置
INSERT INTO `ai_model_configs` (`provider_id`, `model_name`, `model_id`, `display_name`, `description`, `max_tokens`, `temperature`, `top_p`, `system_prompt`, `is_active`, `is_default`, `sort_order`) VALUES
(1, 'deepseek-chat', 'deepseek-chat', 'DeepSeek Chat', 'DeepSeek通用对话模型', 2048, 0.70, 0.90, '你是一个专业的客服助手，请友好、准确地回答用户的问题。', 1, 1, 1),
(1, 'deepseek-coder', 'deepseek-coder', 'DeepSeek Coder', 'DeepSeek代码生成模型', 4096, 0.50, 0.90, '你是一个专业的编程助手，请提供准确的代码解决方案。', 1, 0, 2),
(1, 'deepseek-reasoner', 'deepseek-reasoner', 'DeepSeek Reasoner', 'DeepSeek推理模型', 8192, 0.30, 0.95, '你是一个逻辑推理专家，请提供详细的分析和推理过程。', 1, 0, 3);

-- 插入默认的豆包模型配置
INSERT INTO `ai_model_configs` (`provider_id`, `model_name`, `model_id`, `display_name`, `description`, `max_tokens`, `temperature`, `top_p`, `system_prompt`, `is_active`, `is_default`, `sort_order`) VALUES
(2, 'doubao-lite-4k', 'doubao-lite-4k', '豆包-lite-4k', '豆包轻量级4K上下文模型', 4096, 0.70, 0.90, '你是一个专业的客服助手，请友好、准确地回答用户的问题。', 1, 1, 1),
(2, 'doubao-pro-4k', 'doubao-pro-4k', '豆包-pro-4k', '豆包专业级4K上下文模型', 4096, 0.70, 0.90, '你是一个专业的客服助手，请友好、准确地回答用户的问题。', 1, 0, 2),
(2, 'doubao-pro-32k', 'doubao-pro-32k', '豆包-pro-32k', '豆包专业级32K上下文模型', 32768, 0.70, 0.90, '你是一个专业的客服助手，请友好、准确地回答用户的问题。', 1, 0, 3),
(2, 'doubao-pro-128k', 'doubao-pro-128k', '豆包-pro-128k', '豆包专业级128K上下文模型', 131072, 0.70, 0.90, '你是一个专业的客服助手，请友好、准确地回答用户的问题。', 1, 0, 4);

COMMIT;
SET FOREIGN_KEY_CHECKS = 1;
