<?php
/**
 * 豆包独立API接口
 * 提供完整的豆包AI服务调用功能
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

// 引入基础类
require_once 'api_base.php';

class DoubaoAPI extends ApiBase {
    
    private $providerKey = 'doubao';
    private $providerId;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        $this->providerId = $this->getProviderIdByKey($this->providerKey);
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            // 验证请求方法
            if ($this->method !== 'POST') {
                $this->respondError('Only POST method is allowed', 405);
            }
            
            // 获取请求动作
            $action = $this->request['action'] ?? '';
            
            if (empty($action)) {
                $this->respondError('Action parameter is required', 400);
            }
            
            // 路由到对应的处理方法
            switch ($action) {
                case 'chat':
                    $this->handleChat();
                    break;
                case 'models':
                    $this->getModels();
                    break;
                case 'config':
                    $this->getConfig();
                    break;
                case 'stats':
                    $this->getStats();
                    break;
                case 'test':
                    $this->testConnection();
                    break;
                default:
                    $this->respondError('Unknown action: ' . $action, 400);
            }
            
        } catch (Exception $e) {
            error_log("DoubaoAPI Error: " . $e->getMessage());
            $this->respondError('Internal server error', 500);
        }
    }
    
    /**
     * 处理聊天请求
     */
    private function handleChat() {
        // 验证必要参数
        $message = $this->request['message'] ?? '';
        $model = $this->request['model'] ?? '';
        $conversationId = $this->request['conversation_id'] ?? '';
        
        if (empty($message)) {
            $this->respondError('Message parameter is required', 400);
        }
        
        try {
            // 获取API配置
            $config = $this->getAPIConfig();
            if (!$config) {
                $this->respondError('Doubao API not configured', 400);
            }
            
            // 获取模型配置
            $modelConfig = $this->getModelConfig($model);
            if (!$modelConfig) {
                $this->respondError('Invalid model specified', 400);
            }
            
            // 发送聊天请求
            $response = $this->sendChatRequest($message, $modelConfig, $config, $conversationId);
            
            if ($response['success']) {
                $this->respondSuccess([
                    'message' => $response['message'],
                    'conversation_id' => $response['conversation_id'],
                    'model' => $modelConfig['model_id'],
                    'tokens_used' => $response['tokens_used'],
                    'response_time' => $response['response_time']
                ], 'Chat completed successfully');
            } else {
                $this->respondError('Chat failed: ' . $response['error'], 400);
            }
            
        } catch (Exception $e) {
            error_log("Doubao chat error: " . $e->getMessage());
            $this->respondError('Chat request failed', 500);
        }
    }
    
    /**
     * 获取可用模型
     */
    private function getModels() {
        try {
            $models = $this->fetchAll(
                "SELECT model_id, display_name, description, max_tokens, temperature, 
                        top_p, frequency_penalty, system_prompt, is_default, sort_order
                 FROM ai_model_configs 
                 WHERE provider_id = ? AND is_active = 1 
                 ORDER BY sort_order ASC",
                [$this->providerId]
            );
            
            $this->respondSuccess(['models' => $models], 'Models retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get Doubao models error: " . $e->getMessage());
            $this->respondError('Failed to retrieve models', 500);
        }
    }
    
    /**
     * 获取配置信息
     */
    private function getConfig() {
        try {
            $config = $this->getAPIConfig();
            
            if ($config) {
                // 移除敏感信息
                unset($config['api_key']);
                $this->respondSuccess(['config' => $config], 'Configuration retrieved successfully');
            } else {
                $this->respondError('Doubao API not configured', 400);
            }
            
        } catch (Exception $e) {
            error_log("Get Doubao config error: " . $e->getMessage());
            $this->respondError('Failed to retrieve configuration', 500);
        }
    }
    
    /**
     * 获取使用统计
     */
    private function getStats() {
        try {
            $period = $this->request['period'] ?? 'today';
            $stats = $this->getUsageStats($period);
            
            $this->respondSuccess(['stats' => $stats], 'Statistics retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get Doubao stats error: " . $e->getMessage());
            $this->respondError('Failed to retrieve statistics', 500);
        }
    }
    
    /**
     * 测试连接
     */
    private function testConnection() {
        try {
            $config = $this->getAPIConfig();
            if (!$config) {
                $this->respondError('Doubao API not configured', 400);
            }
            
            // 发送测试请求
            $response = $this->makeHttpRequest($config['base_url'] . '/api/v3/chat/completions', [
                'headers' => [
                    'Authorization: Bearer ' . $config['api_key'],
                    'Content-Type: application/json'
                ],
                'data' => json_encode([
                    'model' => $config['endpoint_id'],
                    'messages' => [
                        ['role' => 'user', 'content' => '测试连接']
                    ],
                    'max_tokens' => 10
                ]),
                'timeout' => 10
            ]);
            
            if ($response['success']) {
                $this->respondSuccess([
                    'status' => 'connected',
                    'endpoint_id' => $config['endpoint_id']
                ], 'Connection test successful');
            } else {
                $this->respondError('Connection test failed: ' . $response['error'], 400);
            }
            
        } catch (Exception $e) {
            error_log("Doubao connection test error: " . $e->getMessage());
            $this->respondError('Connection test failed', 500);
        }
    }
    
    /**
     * 发送聊天请求
     */
    private function sendChatRequest($message, $modelConfig, $config, $conversationId = '') {
        $startTime = microtime(true);
        
        try {
            // 生成对话ID
            if (empty($conversationId)) {
                $conversationId = uniqid('doubao_', true);
            }
            
            // 构建请求数据
            $requestData = [
                'model' => $config['endpoint_id'], // 豆包使用端点ID作为模型
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $modelConfig['system_prompt'] ?? '你是一个专业的AI助手，请友好、准确地回答用户的问题。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $message
                    ]
                ],
                'max_tokens' => $modelConfig['max_tokens'] ?? 2048,
                'temperature' => (float)($modelConfig['temperature'] ?? 0.7),
                'top_p' => (float)($modelConfig['top_p'] ?? 0.9),
                'frequency_penalty' => (float)($modelConfig['frequency_penalty'] ?? 0),
                'stream' => false
            ];
            
            // 发送请求
            $response = $this->makeHttpRequest($config['base_url'] . '/api/v3/chat/completions', [
                'headers' => [
                    'Authorization: Bearer ' . $config['api_key'],
                    'Content-Type: application/json',
                    'User-Agent: XiaoMeiHua-Doubao/1.0'
                ],
                'data' => json_encode($requestData),
                'timeout' => $config['timeout'] ?? 30
            ]);
            
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000); // 毫秒
            
            if ($response['success']) {
                // 解析响应
                $aiMessage = $response['data']['choices'][0]['message']['content'] ?? '';
                $tokensUsed = $response['data']['usage']['total_tokens'] ?? 0;
                
                // 记录对话
                $this->recordConversation([
                    'conversation_id' => $conversationId,
                    'model_id' => $modelConfig['id'],
                    'user_message' => $message,
                    'ai_response' => $aiMessage,
                    'request_data' => $requestData,
                    'response_data' => $response['data'],
                    'tokens_used' => $tokensUsed,
                    'response_time' => $responseTime,
                    'status' => 'success'
                ]);
                
                // 更新统计
                $this->updateUsageStats(true, $responseTime, $tokensUsed);
                
                return [
                    'success' => true,
                    'message' => $aiMessage,
                    'conversation_id' => $conversationId,
                    'tokens_used' => $tokensUsed,
                    'response_time' => $responseTime
                ];
            } else {
                // 记录失败的对话
                $this->recordConversation([
                    'conversation_id' => $conversationId,
                    'model_id' => $modelConfig['id'],
                    'user_message' => $message,
                    'request_data' => $requestData,
                    'response_time' => $responseTime,
                    'status' => 'error',
                    'error_message' => $response['error']
                ]);
                
                // 更新统计
                $this->updateUsageStats(false, $responseTime, 0);
                
                return ['success' => false, 'error' => $response['error']];
            }
            
        } catch (Exception $e) {
            error_log("Doubao send chat request error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 获取API配置
     */
    private function getAPIConfig() {
        $config = $this->fetchOne(
            "SELECT * FROM ai_api_configs WHERE provider_id = ? AND is_default = 1",
            [$this->providerId]
        );
        
        if ($config) {
            $config['api_key'] = base64_decode($config['api_key']); // 解密
        }
        
        return $config;
    }
    
    /**
     * 获取模型配置
     */
    private function getModelConfig($modelId = '') {
        if (empty($modelId)) {
            // 获取默认模型
            return $this->fetchOne(
                "SELECT * FROM ai_model_configs WHERE provider_id = ? AND is_default = 1",
                [$this->providerId]
            );
        } else {
            // 获取指定模型
            return $this->fetchOne(
                "SELECT * FROM ai_model_configs WHERE provider_id = ? AND model_id = ? AND is_active = 1",
                [$this->providerId, $modelId]
            );
        }
    }
    
    /**
     * 获取提供商ID
     */
    private function getProviderIdByKey($providerKey) {
        $provider = $this->fetchOne(
            "SELECT id FROM ai_providers WHERE provider_key = ?",
            [$providerKey]
        );
        
        if (!$provider) {
            throw new Exception("Provider not found: " . $providerKey);
        }
        
        return $provider['id'];
    }
    
    /**
     * 记录对话
     */
    private function recordConversation($data) {
        try {
            $this->safeQuery(
                "INSERT INTO ai_conversations 
                 (conversation_id, provider_id, model_id, user_message, ai_response, 
                  request_data, response_data, tokens_used, response_time, status, 
                  error_message, ip_address, user_agent, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [
                    $data['conversation_id'],
                    $this->providerId,
                    $data['model_id'],
                    $data['user_message'],
                    $data['ai_response'] ?? null,
                    json_encode($data['request_data'] ?? []),
                    json_encode($data['response_data'] ?? []),
                    $data['tokens_used'] ?? 0,
                    $data['response_time'] ?? 0,
                    $data['status'],
                    $data['error_message'] ?? null,
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null
                ]
            );
        } catch (Exception $e) {
            error_log("Record Doubao conversation error: " . $e->getMessage());
        }
    }
    
    /**
     * 更新使用统计
     */
    private function updateUsageStats($success, $responseTime, $tokensUsed) {
        try {
            $today = date('Y-m-d');
            
            // 检查今日统计是否存在
            $existingStats = $this->fetchOne(
                "SELECT id FROM ai_usage_stats WHERE provider_id = ? AND date = ?",
                [$this->providerId, $today]
            );
            
            if ($existingStats) {
                // 更新现有统计
                $this->safeQuery(
                    "UPDATE ai_usage_stats SET 
                     total_requests = total_requests + 1,
                     successful_requests = successful_requests + ?,
                     failed_requests = failed_requests + ?,
                     total_tokens = total_tokens + ?,
                     avg_response_time = (avg_response_time + ?) / 2,
                     updated_at = NOW()
                     WHERE id = ?",
                    [
                        $success ? 1 : 0,
                        $success ? 0 : 1,
                        $tokensUsed,
                        $responseTime,
                        $existingStats['id']
                    ]
                );
            } else {
                // 创建新统计
                $this->safeQuery(
                    "INSERT INTO ai_usage_stats 
                     (provider_id, date, total_requests, successful_requests, failed_requests, 
                      total_tokens, avg_response_time, created_at)
                     VALUES (?, ?, 1, ?, ?, ?, ?, NOW())",
                    [
                        $this->providerId,
                        $today,
                        $success ? 1 : 0,
                        $success ? 0 : 1,
                        $tokensUsed,
                        $responseTime
                    ]
                );
            }
        } catch (Exception $e) {
            error_log("Update Doubao usage stats error: " . $e->getMessage());
        }
    }
    
    /**
     * 获取使用统计
     */
    private function getUsageStats($period = 'today') {
        try {
            $dateCondition = '';
            $params = [$this->providerId];
            
            switch ($period) {
                case 'today':
                    $dateCondition = 'AND date = CURDATE()';
                    break;
                case 'week':
                    $dateCondition = 'AND date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
                    break;
                case 'month':
                    $dateCondition = 'AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
                    break;
            }
            
            $stats = $this->fetchOne(
                "SELECT 
                    COALESCE(SUM(total_requests), 0) as total_requests,
                    COALESCE(SUM(successful_requests), 0) as successful_requests,
                    COALESCE(SUM(failed_requests), 0) as failed_requests,
                    COALESCE(AVG(avg_response_time), 0) as avg_response_time,
                    COALESCE(SUM(total_tokens), 0) as token_usage,
                    COALESCE(SUM(total_cost), 0) as total_cost
                 FROM ai_usage_stats 
                 WHERE provider_id = ? $dateCondition",
                $params
            );
            
            // 计算成功率
            $successRate = 0;
            if ($stats['total_requests'] > 0) {
                $successRate = round(($stats['successful_requests'] / $stats['total_requests']) * 100, 1);
            }
            
            return [
                'total_requests' => (int)$stats['total_requests'],
                'successful_requests' => (int)$stats['successful_requests'],
                'failed_requests' => (int)$stats['failed_requests'],
                'success_rate' => $successRate,
                'avg_response_time' => (int)$stats['avg_response_time'],
                'token_usage' => (int)$stats['token_usage'],
                'total_cost' => (float)$stats['total_cost'],
                'period' => $period
            ];
            
        } catch (Exception $e) {
            error_log("Get Doubao usage stats error: " . $e->getMessage());
            return [
                'total_requests' => 0,
                'successful_requests' => 0,
                'failed_requests' => 0,
                'success_rate' => 0,
                'avg_response_time' => 0,
                'token_usage' => 0,
                'total_cost' => 0,
                'period' => $period
            ];
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeHttpRequest($url, $options = []) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $options['timeout'] ?? 30,
            CURLOPT_HTTPHEADER => $options['headers'] ?? [],
            CURLOPT_POST => isset($options['data']),
            CURLOPT_POSTFIELDS => $options['data'] ?? null,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return ['success' => false, 'error' => $error];
        }
        
        if ($httpCode >= 200 && $httpCode < 300) {
            return ['success' => true, 'data' => json_decode($response, true)];
        } else {
            return ['success' => false, 'error' => "HTTP $httpCode: $response"];
        }
    }
}

// 处理请求
$api = new DoubaoAPI();
$api->handleRequest();
?>
