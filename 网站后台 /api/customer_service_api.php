<?php
/**
 * APP客服API接口
 * 支持DeepSeek和豆包AI服务
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

// 引入基础类
require_once 'api_base.php';

class CustomerServiceAPI extends ApiBase {
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            // 验证请求方法
            if ($this->method !== 'POST') {
                $this->respondError('Only POST method is allowed', 405);
            }
            
            // 获取请求动作
            $action = $this->request['action'] ?? '';
            
            if (empty($action)) {
                $this->respondError('Action parameter is required', 400);
            }
            
            // 路由到对应的处理方法
            switch ($action) {
                // DeepSeek相关接口
                case 'save_deepseek_api_config':
                    $this->saveDeepSeekApiConfig();
                    break;
                case 'save_deepseek_model_config':
                    $this->saveDeepSeekModelConfig();
                    break;
                case 'test_deepseek_connection':
                    $this->testDeepSeekConnection();
                    break;
                case 'test_deepseek_api':
                    $this->testDeepSeekAPI();
                    break;
                case 'get_deepseek_config':
                    $this->getDeepSeekConfig();
                    break;
                case 'get_deepseek_stats':
                    $this->getDeepSeekStats();
                    break;
                
                // 豆包相关接口
                case 'save_doubao_api_config':
                    $this->saveDoubaoApiConfig();
                    break;
                case 'save_doubao_model_config':
                    $this->saveDoubaoModelConfig();
                    break;
                case 'test_doubao_connection':
                    $this->testDoubaoConnection();
                    break;
                case 'test_doubao_api':
                    $this->testDoubaoAPI();
                    break;
                case 'get_doubao_config':
                    $this->getDoubaoConfig();
                    break;
                case 'get_doubao_stats':
                    $this->getDoubaoStats();
                    break;
                
                // 通用接口
                case 'get_available_models':
                    $this->getAvailableModels();
                    break;
                case 'chat':
                    $this->handleChat();
                    break;
                
                default:
                    $this->respondError('Unknown action: ' . $action, 400);
            }
            
        } catch (Exception $e) {
            error_log("CustomerServiceAPI Error: " . $e->getMessage());
            $this->respondError('Internal server error', 500);
        }
    }
    
    /**
     * 保存DeepSeek API配置
     */
    private function saveDeepSeekApiConfig() {
        $config = $this->request['config'] ?? [];
        
        if (empty($config['api_key'])) {
            $this->respondError('API密钥不能为空', 400);
        }
        
        try {
            // 加密API密钥
            $encryptedApiKey = $this->encryptApiKey($config['api_key']);
            
            // 获取DeepSeek提供商ID
            $providerId = $this->getProviderIdByKey('deepseek');
            
            // 检查是否已存在配置
            $existingConfig = $this->fetchOne(
                "SELECT id FROM ai_api_configs WHERE provider_id = ? AND is_default = 1",
                [$providerId]
            );
            
            if ($existingConfig) {
                // 更新现有配置
                $this->safeQuery(
                    "UPDATE ai_api_configs SET 
                     api_key = ?, base_url = ?, timeout = ?, max_retries = ?, updated_at = NOW()
                     WHERE id = ?",
                    [
                        $encryptedApiKey,
                        $config['base_url'] ?? 'https://api.deepseek.com',
                        $config['timeout'] ?? 30,
                        $config['max_retries'] ?? 3,
                        $existingConfig['id']
                    ]
                );
            } else {
                // 创建新配置
                $this->safeQuery(
                    "INSERT INTO ai_api_configs 
                     (provider_id, config_name, api_key, base_url, timeout, max_retries, is_default, created_at)
                     VALUES (?, ?, ?, ?, ?, ?, 1, NOW())",
                    [
                        $providerId,
                        'DeepSeek默认配置',
                        $encryptedApiKey,
                        $config['base_url'] ?? 'https://api.deepseek.com',
                        $config['timeout'] ?? 30,
                        $config['max_retries'] ?? 3
                    ]
                );
            }
            
            $this->respondSuccess([], 'DeepSeek API配置保存成功');
            
        } catch (Exception $e) {
            error_log("Save DeepSeek API config error: " . $e->getMessage());
            $this->respondError('保存配置失败', 500);
        }
    }
    
    /**
     * 保存DeepSeek模型配置
     */
    private function saveDeepSeekModelConfig() {
        $config = $this->request['config'] ?? [];
        
        if (empty($config['model'])) {
            $this->respondError('模型不能为空', 400);
        }
        
        try {
            // 获取DeepSeek提供商ID
            $providerId = $this->getProviderIdByKey('deepseek');
            
            // 更新模型配置
            $this->safeQuery(
                "UPDATE ai_model_configs SET 
                 temperature = ?, max_tokens = ?, system_prompt = ?, updated_at = NOW()
                 WHERE provider_id = ? AND model_id = ?",
                [
                    $config['temperature'] ?? 0.7,
                    $config['max_tokens'] ?? 2048,
                    $config['system_prompt'] ?? '你是一个专业的客服助手，请友好、准确地回答用户的问题。',
                    $providerId,
                    $config['model']
                ]
            );
            
            // 设置为默认模型
            if (isset($config['set_default']) && $config['set_default']) {
                $this->safeQuery(
                    "UPDATE ai_model_configs SET is_default = 0 WHERE provider_id = ?",
                    [$providerId]
                );
                
                $this->safeQuery(
                    "UPDATE ai_model_configs SET is_default = 1 WHERE provider_id = ? AND model_id = ?",
                    [$providerId, $config['model']]
                );
            }
            
            $this->respondSuccess([], 'DeepSeek模型配置保存成功');
            
        } catch (Exception $e) {
            error_log("Save DeepSeek model config error: " . $e->getMessage());
            $this->respondError('保存模型配置失败', 500);
        }
    }
    
    /**
     * 测试DeepSeek连接
     */
    private function testDeepSeekConnection() {
        $apiKey = $this->request['api_key'] ?? '';
        $baseUrl = $this->request['base_url'] ?? 'https://api.deepseek.com';
        
        if (empty($apiKey)) {
            $this->respondError('API密钥不能为空', 400);
        }
        
        try {
            $response = $this->makeHttpRequest($baseUrl . '/v1/models', [
                'headers' => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ],
                'timeout' => 10
            ]);
            
            if ($response['success']) {
                $this->respondSuccess(['models' => $response['data']], 'DeepSeek连接测试成功');
            } else {
                $this->respondError('连接测试失败: ' . $response['error'], 400);
            }
            
        } catch (Exception $e) {
            error_log("Test DeepSeek connection error: " . $e->getMessage());
            $this->respondError('连接测试失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 测试DeepSeek API
     */
    private function testDeepSeekAPI() {
        $message = $this->request['message'] ?? '';
        
        if (empty($message)) {
            $this->respondError('测试消息不能为空', 400);
        }
        
        try {
            // 获取DeepSeek配置
            $config = $this->getAIConfig('deepseek');
            if (!$config) {
                $this->respondError('请先配置DeepSeek API', 400);
            }
            
            // 发送测试请求
            $response = $this->sendChatRequest('deepseek', $message, $config);
            
            if ($response['success']) {
                $this->respondSuccess(['response' => $response['message']], 'DeepSeek API测试成功');
            } else {
                $this->respondError('API测试失败: ' . $response['error'], 400);
            }
            
        } catch (Exception $e) {
            error_log("Test DeepSeek API error: " . $e->getMessage());
            $this->respondError('API测试失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取DeepSeek配置
     */
    private function getDeepSeekConfig() {
        try {
            $config = $this->getAIConfig('deepseek');
            
            if ($config) {
                // 不返回敏感信息
                unset($config['api_key']);
                $this->respondSuccess(['config' => $config], '获取配置成功');
            } else {
                $this->respondSuccess(['config' => null], '暂无配置');
            }
            
        } catch (Exception $e) {
            error_log("Get DeepSeek config error: " . $e->getMessage());
            $this->respondError('获取配置失败', 500);
        }
    }
    
    /**
     * 获取DeepSeek统计数据
     */
    private function getDeepSeekStats() {
        try {
            $providerId = $this->getProviderIdByKey('deepseek');
            $stats = $this->getUsageStats($providerId);
            
            $this->respondSuccess(['stats' => $stats], '获取统计数据成功');
            
        } catch (Exception $e) {
            error_log("Get DeepSeek stats error: " . $e->getMessage());
            $this->respondError('获取统计数据失败', 500);
        }
    }
    
    /**
     * 保存豆包API配置
     */
    private function saveDoubaoApiConfig() {
        $config = $this->request['config'] ?? [];

        if (empty($config['api_key']) || empty($config['endpoint_id'])) {
            $this->respondError('API密钥和端点ID不能为空', 400);
        }

        try {
            // 加密API密钥
            $encryptedApiKey = $this->encryptApiKey($config['api_key']);

            // 获取豆包提供商ID
            $providerId = $this->getProviderIdByKey('doubao');

            // 检查是否已存在配置
            $existingConfig = $this->fetchOne(
                "SELECT id FROM ai_api_configs WHERE provider_id = ? AND is_default = 1",
                [$providerId]
            );

            if ($existingConfig) {
                // 更新现有配置
                $this->safeQuery(
                    "UPDATE ai_api_configs SET
                     api_key = ?, base_url = ?, endpoint_id = ?, timeout = ?, max_retries = ?, updated_at = NOW()
                     WHERE id = ?",
                    [
                        $encryptedApiKey,
                        $config['base_url'] ?? 'https://ark.cn-beijing.volces.com',
                        $config['endpoint_id'],
                        $config['timeout'] ?? 30,
                        $config['max_retries'] ?? 3,
                        $existingConfig['id']
                    ]
                );
            } else {
                // 创建新配置
                $this->safeQuery(
                    "INSERT INTO ai_api_configs
                     (provider_id, config_name, api_key, base_url, endpoint_id, timeout, max_retries, is_default, created_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW())",
                    [
                        $providerId,
                        '豆包默认配置',
                        $encryptedApiKey,
                        $config['base_url'] ?? 'https://ark.cn-beijing.volces.com',
                        $config['endpoint_id'],
                        $config['timeout'] ?? 30,
                        $config['max_retries'] ?? 3
                    ]
                );
            }

            $this->respondSuccess([], '豆包API配置保存成功');

        } catch (Exception $e) {
            error_log("Save Doubao API config error: " . $e->getMessage());
            $this->respondError('保存配置失败', 500);
        }
    }

    /**
     * 保存豆包模型配置
     */
    private function saveDoubaoModelConfig() {
        $config = $this->request['config'] ?? [];

        if (empty($config['model'])) {
            $this->respondError('模型不能为空', 400);
        }

        try {
            // 获取豆包提供商ID
            $providerId = $this->getProviderIdByKey('doubao');

            // 更新模型配置
            $this->safeQuery(
                "UPDATE ai_model_configs SET
                 temperature = ?, max_tokens = ?, top_p = ?, frequency_penalty = ?, system_prompt = ?, updated_at = NOW()
                 WHERE provider_id = ? AND model_id = ?",
                [
                    $config['temperature'] ?? 0.7,
                    $config['max_tokens'] ?? 2048,
                    $config['top_p'] ?? 0.9,
                    $config['frequency_penalty'] ?? 0,
                    $config['system_prompt'] ?? '你是一个专业的客服助手，请友好、准确地回答用户的问题。',
                    $providerId,
                    $config['model']
                ]
            );

            // 设置为默认模型
            if (isset($config['set_default']) && $config['set_default']) {
                $this->safeQuery(
                    "UPDATE ai_model_configs SET is_default = 0 WHERE provider_id = ?",
                    [$providerId]
                );

                $this->safeQuery(
                    "UPDATE ai_model_configs SET is_default = 1 WHERE provider_id = ? AND model_id = ?",
                    [$providerId, $config['model']]
                );
            }

            $this->respondSuccess([], '豆包模型配置保存成功');

        } catch (Exception $e) {
            error_log("Save Doubao model config error: " . $e->getMessage());
            $this->respondError('保存模型配置失败', 500);
        }
    }

    /**
     * 测试豆包连接
     */
    private function testDoubaoConnection() {
        $apiKey = $this->request['api_key'] ?? '';
        $baseUrl = $this->request['base_url'] ?? 'https://ark.cn-beijing.volces.com';
        $endpointId = $this->request['endpoint_id'] ?? '';

        if (empty($apiKey) || empty($endpointId)) {
            $this->respondError('API密钥和端点ID不能为空', 400);
        }

        try {
            $response = $this->makeHttpRequest($baseUrl . '/api/v3/chat/completions', [
                'headers' => [
                    'Authorization: Bearer ' . $apiKey,
                    'Content-Type: application/json'
                ],
                'data' => json_encode([
                    'model' => $endpointId,
                    'messages' => [
                        ['role' => 'user', 'content' => '测试连接']
                    ],
                    'max_tokens' => 10
                ]),
                'timeout' => 10
            ]);

            if ($response['success']) {
                $this->respondSuccess(['response' => $response['data']], '豆包连接测试成功');
            } else {
                $this->respondError('连接测试失败: ' . $response['error'], 400);
            }

        } catch (Exception $e) {
            error_log("Test Doubao connection error: " . $e->getMessage());
            $this->respondError('连接测试失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 测试豆包API
     */
    private function testDoubaoAPI() {
        $message = $this->request['message'] ?? '';

        if (empty($message)) {
            $this->respondError('测试消息不能为空', 400);
        }

        try {
            // 获取豆包配置
            $config = $this->getAIConfig('doubao');
            if (!$config) {
                $this->respondError('请先配置豆包API', 400);
            }

            // 发送测试请求
            $response = $this->sendChatRequest('doubao', $message, $config);

            if ($response['success']) {
                $this->respondSuccess(['response' => $response['message']], '豆包API测试成功');
            } else {
                $this->respondError('API测试失败: ' . $response['error'], 400);
            }

        } catch (Exception $e) {
            error_log("Test Doubao API error: " . $e->getMessage());
            $this->respondError('API测试失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取豆包配置
     */
    private function getDoubaoConfig() {
        try {
            $config = $this->getAIConfig('doubao');

            if ($config) {
                // 不返回敏感信息
                unset($config['api_key']);
                $this->respondSuccess(['config' => $config], '获取配置成功');
            } else {
                $this->respondSuccess(['config' => null], '暂无配置');
            }

        } catch (Exception $e) {
            error_log("Get Doubao config error: " . $e->getMessage());
            $this->respondError('获取配置失败', 500);
        }
    }

    /**
     * 获取豆包统计数据
     */
    private function getDoubaoStats() {
        try {
            $providerId = $this->getProviderIdByKey('doubao');
            $stats = $this->getUsageStats($providerId);

            $this->respondSuccess(['stats' => $stats], '获取统计数据成功');

        } catch (Exception $e) {
            error_log("Get Doubao stats error: " . $e->getMessage());
            $this->respondError('获取统计数据失败', 500);
        }
    }

    /**
     * 获取可用模型
     */
    private function getAvailableModels() {
        $provider = $this->request['provider'] ?? '';

        if (empty($provider)) {
            $this->respondError('提供商参数不能为空', 400);
        }

        try {
            $providerId = $this->getProviderIdByKey($provider);

            $models = $this->fetchAll(
                "SELECT model_id as id, display_name as name, description
                 FROM ai_model_configs
                 WHERE provider_id = ? AND is_active = 1
                 ORDER BY sort_order ASC",
                [$providerId]
            );

            $this->respondSuccess(['models' => $models], '获取模型列表成功');

        } catch (Exception $e) {
            error_log("Get available models error: " . $e->getMessage());
            $this->respondError('获取模型列表失败', 500);
        }
    }

    /**
     * 处理聊天请求
     */
    private function handleChat() {
        $provider = $this->request['provider'] ?? '';
        $message = $this->request['message'] ?? '';
        $conversationId = $this->request['conversation_id'] ?? '';

        if (empty($provider) || empty($message)) {
            $this->respondError('提供商和消息不能为空', 400);
        }

        try {
            // 获取配置
            $config = $this->getAIConfig($provider);
            if (!$config) {
                $this->respondError('请先配置' . $provider . ' API', 400);
            }

            // 发送聊天请求
            $response = $this->sendChatRequest($provider, $message, $config, $conversationId);

            if ($response['success']) {
                $this->respondSuccess([
                    'message' => $response['message'],
                    'conversation_id' => $response['conversation_id'],
                    'tokens_used' => $response['tokens_used'] ?? 0
                ], '聊天成功');
            } else {
                $this->respondError('聊天失败: ' . $response['error'], 400);
            }

        } catch (Exception $e) {
            error_log("Handle chat error: " . $e->getMessage());
            $this->respondError('聊天失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取提供商ID
     */
    private function getProviderIdByKey($providerKey) {
        $provider = $this->fetchOne(
            "SELECT id FROM ai_providers WHERE provider_key = ?",
            [$providerKey]
        );
        
        if (!$provider) {
            throw new Exception("Provider not found: " . $providerKey);
        }
        
        return $provider['id'];
    }
    
    /**
     * 加密API密钥
     */
    private function encryptApiKey($apiKey) {
        // 使用简单的base64编码，实际应用中应使用更安全的加密方法
        return base64_encode($apiKey);
    }
    
    /**
     * 解密API密钥
     */
    private function decryptApiKey($encryptedApiKey) {
        return base64_decode($encryptedApiKey);
    }
    
    /**
     * 获取AI配置
     */
    private function getAIConfig($providerKey) {
        $providerId = $this->getProviderIdByKey($providerKey);
        
        $config = $this->fetchOne(
            "SELECT * FROM ai_api_configs WHERE provider_id = ? AND is_default = 1",
            [$providerId]
        );
        
        if ($config) {
            $config['api_key'] = $this->decryptApiKey($config['api_key']);
        }
        
        return $config;
    }
    
    /**
     * 获取使用统计
     */
    private function getUsageStats($providerId) {
        // 获取今日统计
        $todayStats = $this->fetchOne(
            "SELECT 
                COALESCE(SUM(total_requests), 0) as total_requests,
                COALESCE(SUM(successful_requests), 0) as successful_requests,
                COALESCE(SUM(failed_requests), 0) as failed_requests,
                COALESCE(AVG(avg_response_time), 0) as avg_response_time,
                COALESCE(SUM(total_tokens), 0) as token_usage
             FROM ai_usage_stats 
             WHERE provider_id = ? AND date = CURDATE()",
            [$providerId]
        );
        
        // 计算成功率
        $successRate = 0;
        if ($todayStats['total_requests'] > 0) {
            $successRate = round(($todayStats['successful_requests'] / $todayStats['total_requests']) * 100, 1);
        }
        
        return [
            'total_requests' => (int)$todayStats['total_requests'],
            'success_rate' => $successRate,
            'avg_response_time' => (int)$todayStats['avg_response_time'],
            'token_usage' => (int)$todayStats['token_usage']
        ];
    }
    
    /**
     * 发送聊天请求
     */
    private function sendChatRequest($provider, $message, $config, $conversationId = '') {
        $startTime = microtime(true);

        try {
            // 生成对话ID
            if (empty($conversationId)) {
                $conversationId = uniqid('conv_', true);
            }

            // 获取模型配置
            $modelConfig = $this->getDefaultModelConfig($provider);
            if (!$modelConfig) {
                return ['success' => false, 'error' => '未找到默认模型配置'];
            }

            // 构建请求数据
            $requestData = $this->buildChatRequestData($provider, $message, $modelConfig, $config);

            // 发送请求
            $url = $this->buildChatUrl($provider, $config);
            $response = $this->makeHttpRequest($url, [
                'headers' => $this->buildChatHeaders($provider, $config),
                'data' => json_encode($requestData),
                'timeout' => $config['timeout'] ?? 30
            ]);

            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000); // 毫秒

            if ($response['success']) {
                // 解析响应
                $aiMessage = $this->parseAIResponse($provider, $response['data']);
                $tokensUsed = $this->extractTokensUsed($provider, $response['data']);

                // 记录对话
                $this->recordConversation([
                    'conversation_id' => $conversationId,
                    'provider' => $provider,
                    'model_id' => $modelConfig['id'],
                    'user_message' => $message,
                    'ai_response' => $aiMessage,
                    'request_data' => $requestData,
                    'response_data' => $response['data'],
                    'tokens_used' => $tokensUsed,
                    'response_time' => $responseTime,
                    'status' => 'success'
                ]);

                // 更新统计
                $this->updateUsageStats($provider, true, $responseTime, $tokensUsed);

                return [
                    'success' => true,
                    'message' => $aiMessage,
                    'conversation_id' => $conversationId,
                    'tokens_used' => $tokensUsed
                ];
            } else {
                // 记录失败的对话
                $this->recordConversation([
                    'conversation_id' => $conversationId,
                    'provider' => $provider,
                    'model_id' => $modelConfig['id'],
                    'user_message' => $message,
                    'request_data' => $requestData,
                    'response_time' => $responseTime,
                    'status' => 'error',
                    'error_message' => $response['error']
                ]);

                // 更新统计
                $this->updateUsageStats($provider, false, $responseTime, 0);

                return ['success' => false, 'error' => $response['error']];
            }

        } catch (Exception $e) {
            error_log("Send chat request error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * 获取默认模型配置
     */
    private function getDefaultModelConfig($provider) {
        $providerId = $this->getProviderIdByKey($provider);

        return $this->fetchOne(
            "SELECT * FROM ai_model_configs WHERE provider_id = ? AND is_default = 1",
            [$providerId]
        );
    }

    /**
     * 构建聊天请求数据
     */
    private function buildChatRequestData($provider, $message, $modelConfig, $config) {
        $requestData = [
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $modelConfig['system_prompt'] ?? '你是一个专业的客服助手，请友好、准确地回答用户的问题。'
                ],
                [
                    'role' => 'user',
                    'content' => $message
                ]
            ],
            'max_tokens' => $modelConfig['max_tokens'] ?? 2048,
            'temperature' => (float)($modelConfig['temperature'] ?? 0.7),
            'stream' => false
        ];

        if ($provider === 'deepseek') {
            $requestData['model'] = $modelConfig['model_id'];
        } elseif ($provider === 'doubao') {
            $requestData['model'] = $config['endpoint_id'];
            $requestData['top_p'] = (float)($modelConfig['top_p'] ?? 0.9);
            $requestData['frequency_penalty'] = (float)($modelConfig['frequency_penalty'] ?? 0);
        }

        return $requestData;
    }

    /**
     * 构建聊天URL
     */
    private function buildChatUrl($provider, $config) {
        $baseUrl = $config['base_url'];

        if ($provider === 'deepseek') {
            return $baseUrl . '/v1/chat/completions';
        } elseif ($provider === 'doubao') {
            return $baseUrl . '/api/v3/chat/completions';
        }

        throw new Exception("Unsupported provider: " . $provider);
    }

    /**
     * 构建聊天请求头
     */
    private function buildChatHeaders($provider, $config) {
        return [
            'Authorization: Bearer ' . $config['api_key'],
            'Content-Type: application/json',
            'User-Agent: XiaoMeiHua-CustomerService/1.0'
        ];
    }

    /**
     * 解析AI响应
     */
    private function parseAIResponse($provider, $responseData) {
        if (isset($responseData['choices'][0]['message']['content'])) {
            return $responseData['choices'][0]['message']['content'];
        }

        throw new Exception("Invalid response format");
    }

    /**
     * 提取Token使用量
     */
    private function extractTokensUsed($provider, $responseData) {
        if (isset($responseData['usage']['total_tokens'])) {
            return $responseData['usage']['total_tokens'];
        }

        return 0;
    }

    /**
     * 记录对话
     */
    private function recordConversation($data) {
        try {
            $this->safeQuery(
                "INSERT INTO ai_conversations
                 (conversation_id, provider_id, model_id, user_message, ai_response,
                  request_data, response_data, tokens_used, response_time, status,
                  error_message, ip_address, user_agent, created_at)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [
                    $data['conversation_id'],
                    $this->getProviderIdByKey($data['provider']),
                    $data['model_id'],
                    $data['user_message'],
                    $data['ai_response'] ?? null,
                    json_encode($data['request_data'] ?? []),
                    json_encode($data['response_data'] ?? []),
                    $data['tokens_used'] ?? 0,
                    $data['response_time'] ?? 0,
                    $data['status'],
                    $data['error_message'] ?? null,
                    $_SERVER['REMOTE_ADDR'] ?? null,
                    $_SERVER['HTTP_USER_AGENT'] ?? null
                ]
            );
        } catch (Exception $e) {
            error_log("Record conversation error: " . $e->getMessage());
        }
    }

    /**
     * 更新使用统计
     */
    private function updateUsageStats($provider, $success, $responseTime, $tokensUsed) {
        try {
            $providerId = $this->getProviderIdByKey($provider);
            $today = date('Y-m-d');

            // 检查今日统计是否存在
            $existingStats = $this->fetchOne(
                "SELECT id FROM ai_usage_stats WHERE provider_id = ? AND date = ?",
                [$providerId, $today]
            );

            if ($existingStats) {
                // 更新现有统计
                $this->safeQuery(
                    "UPDATE ai_usage_stats SET
                     total_requests = total_requests + 1,
                     successful_requests = successful_requests + ?,
                     failed_requests = failed_requests + ?,
                     total_tokens = total_tokens + ?,
                     avg_response_time = (avg_response_time + ?) / 2,
                     updated_at = NOW()
                     WHERE id = ?",
                    [
                        $success ? 1 : 0,
                        $success ? 0 : 1,
                        $tokensUsed,
                        $responseTime,
                        $existingStats['id']
                    ]
                );
            } else {
                // 创建新统计
                $this->safeQuery(
                    "INSERT INTO ai_usage_stats
                     (provider_id, date, total_requests, successful_requests, failed_requests,
                      total_tokens, avg_response_time, created_at)
                     VALUES (?, ?, 1, ?, ?, ?, ?, NOW())",
                    [
                        $providerId,
                        $today,
                        $success ? 1 : 0,
                        $success ? 0 : 1,
                        $tokensUsed,
                        $responseTime
                    ]
                );
            }
        } catch (Exception $e) {
            error_log("Update usage stats error: " . $e->getMessage());
        }
    }

    /**
     * 发送HTTP请求
     */
    private function makeHttpRequest($url, $options = []) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $options['timeout'] ?? 30,
            CURLOPT_HTTPHEADER => $options['headers'] ?? [],
            CURLOPT_POST => isset($options['data']),
            CURLOPT_POSTFIELDS => $options['data'] ?? null,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return ['success' => false, 'error' => $error];
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            return ['success' => true, 'data' => json_decode($response, true)];
        } else {
            return ['success' => false, 'error' => "HTTP $httpCode: $response"];
        }
    }
}

// 处理请求
$api = new CustomerServiceAPI();
$api->handleRequest();
?>
