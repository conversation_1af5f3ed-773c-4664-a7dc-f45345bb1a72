<?php
// DeepSeek API配置模块
?>

<div class="deepseek-module">
    <div class="module-header">
        <h2><i class="fas fa-brain"></i> DeepSeek AI配置</h2>
        <p>配置DeepSeek API接口和模型参数</p>
    </div>

    <div class="config-sections">
        <!-- API配置区域 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-key"></i> API配置</h3>
                <button class="btn btn-primary" onclick="testDeepSeekConnection()">
                    <i class="fas fa-plug"></i> 测试连接
                </button>
            </div>
            
            <form id="deepseek-api-form" class="config-form">
                <div class="form-group">
                    <label for="deepseek_api_key">API密钥</label>
                    <div class="input-group">
                        <input type="password" id="deepseek_api_key" name="api_key" 
                               placeholder="请输入DeepSeek API密钥" required>
                        <button type="button" class="btn-toggle-password" onclick="togglePassword('deepseek_api_key')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="form-text">从DeepSeek官网获取您的API密钥</small>
                </div>

                <div class="form-group">
                    <label for="deepseek_base_url">API基础URL</label>
                    <input type="url" id="deepseek_base_url" name="base_url" 
                           value="https://api.deepseek.com" placeholder="https://api.deepseek.com">
                    <small class="form-text">DeepSeek API的基础URL地址</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="deepseek_timeout">请求超时(秒)</label>
                        <input type="number" id="deepseek_timeout" name="timeout" 
                               value="30" min="5" max="120">
                    </div>
                    <div class="form-group">
                        <label for="deepseek_max_retries">最大重试次数</label>
                        <input type="number" id="deepseek_max_retries" name="max_retries" 
                               value="3" min="0" max="10">
                    </div>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> 保存配置
                </button>
            </form>
        </div>

        <!-- 模型配置区域 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-cogs"></i> 模型配置</h3>
                <button class="btn btn-info" onclick="loadAvailableModels('deepseek')">
                    <i class="fas fa-sync"></i> 刷新模型列表
                </button>
            </div>
            
            <form id="deepseek-model-form" class="config-form">
                <div class="form-group">
                    <label for="deepseek_model">选择模型</label>
                    <select id="deepseek_model" name="model" required>
                        <option value="">请选择模型</option>
                        <option value="deepseek-chat">deepseek-chat</option>
                        <option value="deepseek-coder">deepseek-coder</option>
                        <option value="deepseek-reasoner">deepseek-reasoner</option>
                    </select>
                    <small class="form-text">选择要使用的DeepSeek模型</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="deepseek_temperature">温度值</label>
                        <input type="range" id="deepseek_temperature" name="temperature" 
                               min="0" max="2" step="0.1" value="0.7">
                        <span class="range-value">0.7</span>
                    </div>
                    <div class="form-group">
                        <label for="deepseek_max_tokens">最大Token数</label>
                        <input type="number" id="deepseek_max_tokens" name="max_tokens" 
                               value="2048" min="1" max="8192">
                    </div>
                </div>

                <div class="form-group">
                    <label for="deepseek_system_prompt">系统提示词</label>
                    <textarea id="deepseek_system_prompt" name="system_prompt" rows="4" 
                              placeholder="请输入系统提示词，定义AI的角色和行为...">你是一个专业的客服助手，请友好、准确地回答用户的问题。</textarea>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> 保存模型配置
                </button>
            </form>
        </div>

        <!-- 测试区域 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-vial"></i> 接口测试</h3>
            </div>
            
            <div class="test-area">
                <div class="form-group">
                    <label for="deepseek_test_message">测试消息</label>
                    <textarea id="deepseek_test_message" rows="3" 
                              placeholder="输入测试消息...">你好，请介绍一下你自己。</textarea>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testDeepSeekAPI()">
                    <i class="fas fa-paper-plane"></i> 发送测试
                </button>
                
                <div id="deepseek_test_result" class="test-result" style="display: none;">
                    <h4>测试结果：</h4>
                    <div class="result-content"></div>
                </div>
            </div>
        </div>

        <!-- 使用统计 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-chart-line"></i> 使用统计</h3>
                <button class="btn btn-secondary" onclick="refreshDeepSeekStats()">
                    <i class="fas fa-refresh"></i> 刷新数据
                </button>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="deepseek_total_requests">0</h4>
                        <p>总请求数</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="deepseek_success_rate">0%</h4>
                        <p>成功率</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="deepseek_avg_response_time">0ms</h4>
                        <p>平均响应时间</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="deepseek_token_usage">0</h4>
                        <p>Token使用量</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.deepseek-module {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.module-header {
    text-align: center;
    margin-bottom: 30px;
}

.module-header h2 {
    color: white;
    font-size: 24px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.module-header p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.config-sections {
    display: grid;
    gap: 25px;
}

.config-section {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
    color: white;
    font-size: 18px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-form {
    display: grid;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-group {
    position: relative;
    display: flex;
}

.input-group input {
    flex: 1;
    border-radius: 8px 0 0 8px;
}

.btn-toggle-password {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-left: none;
    border-radius: 0 8px 8px 0;
    padding: 12px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-toggle-password:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 4px;
}

.range-value {
    color: #4f46e5;
    font-weight: 600;
    margin-left: 10px;
}

.btn {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.btn-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.test-area {
    display: grid;
    gap: 15px;
}

.test-result {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-result h4 {
    color: white;
    margin: 0 0 10px 0;
    font-size: 16px;
}

.result-content {
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.stat-icon {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-info h4 {
    color: white;
    font-size: 24px;
    margin: 0;
    font-weight: 700;
}

.stat-info p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-size: 14px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>
