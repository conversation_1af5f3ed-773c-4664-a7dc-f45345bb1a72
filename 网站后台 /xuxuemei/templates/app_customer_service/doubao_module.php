<?php
// 豆包 API配置模块
?>

<div class="doubao-module">
    <div class="module-header">
        <h2><i class="fas fa-robot"></i> 豆包AI配置</h2>
        <p>配置豆包API接口和模型参数</p>
    </div>

    <div class="config-sections">
        <!-- API配置区域 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-key"></i> API配置</h3>
                <button class="btn btn-primary" onclick="testDoubaoConnection()">
                    <i class="fas fa-plug"></i> 测试连接
                </button>
            </div>
            
            <form id="doubao-api-form" class="config-form">
                <div class="form-group">
                    <label for="doubao_api_key">API密钥</label>
                    <div class="input-group">
                        <input type="password" id="doubao_api_key" name="api_key" 
                               placeholder="请输入豆包API密钥" required>
                        <button type="button" class="btn-toggle-password" onclick="togglePassword('doubao_api_key')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="form-text">从字节跳动火山引擎获取您的API密钥</small>
                </div>

                <div class="form-group">
                    <label for="doubao_base_url">API基础URL</label>
                    <input type="url" id="doubao_base_url" name="base_url" 
                           value="https://ark.cn-beijing.volces.com" placeholder="https://ark.cn-beijing.volces.com">
                    <small class="form-text">豆包API的基础URL地址</small>
                </div>

                <div class="form-group">
                    <label for="doubao_endpoint_id">端点ID</label>
                    <input type="text" id="doubao_endpoint_id" name="endpoint_id" 
                           placeholder="请输入豆包模型端点ID" required>
                    <small class="form-text">在火山引擎控制台获取的端点ID</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="doubao_timeout">请求超时(秒)</label>
                        <input type="number" id="doubao_timeout" name="timeout" 
                               value="30" min="5" max="120">
                    </div>
                    <div class="form-group">
                        <label for="doubao_max_retries">最大重试次数</label>
                        <input type="number" id="doubao_max_retries" name="max_retries" 
                               value="3" min="0" max="10">
                    </div>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> 保存配置
                </button>
            </form>
        </div>

        <!-- 模型配置区域 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-cogs"></i> 模型配置</h3>
                <button class="btn btn-info" onclick="loadAvailableModels('doubao')">
                    <i class="fas fa-sync"></i> 刷新模型列表
                </button>
            </div>
            
            <form id="doubao-model-form" class="config-form">
                <div class="form-group">
                    <label for="doubao_model">选择模型</label>
                    <select id="doubao_model" name="model" required>
                        <option value="">请选择模型</option>
                        <option value="doubao-lite-4k">豆包-lite-4k</option>
                        <option value="doubao-pro-4k">豆包-pro-4k</option>
                        <option value="doubao-pro-32k">豆包-pro-32k</option>
                        <option value="doubao-pro-128k">豆包-pro-128k</option>
                    </select>
                    <small class="form-text">选择要使用的豆包模型</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="doubao_temperature">温度值</label>
                        <input type="range" id="doubao_temperature" name="temperature" 
                               min="0" max="2" step="0.1" value="0.7">
                        <span class="range-value">0.7</span>
                    </div>
                    <div class="form-group">
                        <label for="doubao_max_tokens">最大Token数</label>
                        <input type="number" id="doubao_max_tokens" name="max_tokens" 
                               value="2048" min="1" max="8192">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="doubao_top_p">Top P</label>
                        <input type="range" id="doubao_top_p" name="top_p" 
                               min="0" max="1" step="0.01" value="0.9">
                        <span class="range-value">0.9</span>
                    </div>
                    <div class="form-group">
                        <label for="doubao_frequency_penalty">频率惩罚</label>
                        <input type="range" id="doubao_frequency_penalty" name="frequency_penalty" 
                               min="0" max="2" step="0.1" value="0">
                        <span class="range-value">0</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="doubao_system_prompt">系统提示词</label>
                    <textarea id="doubao_system_prompt" name="system_prompt" rows="4" 
                              placeholder="请输入系统提示词，定义AI的角色和行为...">你是一个专业的客服助手，请友好、准确地回答用户的问题。</textarea>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save"></i> 保存模型配置
                </button>
            </form>
        </div>

        <!-- 测试区域 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-vial"></i> 接口测试</h3>
            </div>
            
            <div class="test-area">
                <div class="form-group">
                    <label for="doubao_test_message">测试消息</label>
                    <textarea id="doubao_test_message" rows="3" 
                              placeholder="输入测试消息...">你好，请介绍一下你自己。</textarea>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testDoubaoAPI()">
                    <i class="fas fa-paper-plane"></i> 发送测试
                </button>
                
                <div id="doubao_test_result" class="test-result" style="display: none;">
                    <h4>测试结果：</h4>
                    <div class="result-content"></div>
                </div>
            </div>
        </div>

        <!-- 使用统计 -->
        <div class="config-section">
            <div class="section-header">
                <h3><i class="fas fa-chart-line"></i> 使用统计</h3>
                <button class="btn btn-secondary" onclick="refreshDoubaoStats()">
                    <i class="fas fa-refresh"></i> 刷新数据
                </button>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon doubao-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="doubao_total_requests">0</h4>
                        <p>总请求数</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon doubao-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="doubao_success_rate">0%</h4>
                        <p>成功率</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon doubao-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="doubao_avg_response_time">0ms</h4>
                        <p>平均响应时间</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon doubao-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="doubao_token_usage">0</h4>
                        <p>Token使用量</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.doubao-module {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.doubao-module .module-header h2 {
    color: white;
    font-size: 24px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.doubao-module .module-header p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.doubao-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

/* 豆包特色的渐变色 */
.doubao-module .btn-primary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.doubao-module .btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.doubao-module .btn-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.doubao-module .btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.doubao-module .form-group input:focus,
.doubao-module .form-group select:focus,
.doubao-module .form-group textarea:focus {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.doubao-module .range-value {
    color: #f59e0b;
    font-weight: 600;
    margin-left: 10px;
}

/* 继承DeepSeek模块的其他样式 */
.doubao-module .config-sections {
    display: grid;
    gap: 25px;
}

.doubao-module .config-section {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.doubao-module .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.doubao-module .section-header h3 {
    color: white;
    font-size: 18px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.doubao-module .config-form {
    display: grid;
    gap: 20px;
}

.doubao-module .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.doubao-module .form-group label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 14px;
}

.doubao-module .form-group input,
.doubao-module .form-group select,
.doubao-module .form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.doubao-module .form-group input::placeholder,
.doubao-module .form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.doubao-module .input-group {
    position: relative;
    display: flex;
}

.doubao-module .input-group input {
    flex: 1;
    border-radius: 8px 0 0 8px;
}

.doubao-module .btn-toggle-password {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-left: none;
    border-radius: 0 8px 8px 0;
    padding: 12px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.doubao-module .btn-toggle-password:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.doubao-module .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.doubao-module .form-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 4px;
}

.doubao-module .btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.doubao-module .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.doubao-module .test-area {
    display: grid;
    gap: 15px;
}

.doubao-module .test-result {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.doubao-module .test-result h4 {
    color: white;
    margin: 0 0 10px 0;
    font-size: 16px;
}

.doubao-module .result-content {
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
}

.doubao-module .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.doubao-module .stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.doubao-module .stat-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.doubao-module .stat-icon {
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.doubao-module .stat-info h4 {
    color: white;
    font-size: 24px;
    margin: 0;
    font-weight: 700;
}

.doubao-module .stat-info p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    font-size: 14px;
}

@media (max-width: 768px) {
    .doubao-module .form-row {
        grid-template-columns: 1fr;
    }
    
    .doubao-module .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .doubao-module .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>
