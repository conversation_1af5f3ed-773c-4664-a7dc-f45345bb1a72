<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 获取当前选中的标签页
$current_tab = $_GET['tab'] ?? 'deepseek';
?>

<div class="app-customer-service-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-headset"></i> APP客服</h1>
        <p>管理DeepSeek和豆包AI客服接口配置</p>
    </div>

    <!-- 顶部导航标签 -->
    <div class="app-customer-service-nav">
        <div class="nav-tabs">
            <button class="nav-tab <?php echo $current_tab === 'deepseek' ? 'active' : ''; ?>" 
                    onclick="switchTab('deepseek')" data-tab="deepseek">
                <i class="fas fa-brain"></i>
                <span>DeepSeek</span>
            </button>
            <button class="nav-tab <?php echo $current_tab === 'doubao' ? 'active' : ''; ?>" 
                    onclick="switchTab('doubao')" data-tab="doubao">
                <i class="fas fa-robot"></i>
                <span>豆包</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="app-customer-service-content">
        <!-- DeepSeek模块 -->
        <div id="deepseek-content" class="tab-content <?php echo $current_tab === 'deepseek' ? 'active' : ''; ?>">
            <?php include 'app_customer_service/deepseek_module.php'; ?>
        </div>

        <!-- 豆包模块 -->
        <div id="doubao-content" class="tab-content <?php echo $current_tab === 'doubao' ? 'active' : ''; ?>">
            <?php include 'app_customer_service/doubao_module.php'; ?>
        </div>
    </div>
</div>

<!-- APP客服专用样式 -->
<style>
.app-customer-service-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

/* 顶部导航标签 */
.app-customer-service-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 8px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs {
    display: flex;
    gap: 4px;
}

.nav-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.nav-tab.active {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.nav-tab i {
    font-size: 16px;
}

/* 内容区域 */
.app-customer-service-content {
    position: relative;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-customer-service-container {
        padding: 15px;
    }
    
    .nav-tabs {
        flex-direction: column;
        gap: 8px;
    }
    
    .nav-tab {
        padding: 12px 16px;
    }
    
    .page-header h1 {
        font-size: 24px;
    }
}
</style>

<!-- APP客服专用JavaScript -->
<script>
// 标签切换功能
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 激活当前标签
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-content`).classList.add('active');
    
    // 更新URL参数
    const url = new URL(window.location);
    url.searchParams.set('tab', tabName);
    window.history.pushState({}, '', url);
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('tabChanged', { detail: { tab: tabName } }));
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('APP客服页面加载完成');

    // 监听浏览器后退/前进按钮
    window.addEventListener('popstate', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab') || 'deepseek';
        switchTab(tab);
    });

    // 初始化滑块事件
    initializeRangeSliders();

    // 初始化表单提交事件
    initializeFormSubmissions();

    // 加载保存的配置
    loadSavedConfigurations();
});

// 初始化滑块事件
function initializeRangeSliders() {
    const rangeInputs = document.querySelectorAll('input[type="range"]');
    rangeInputs.forEach(input => {
        const valueSpan = input.nextElementSibling;
        if (valueSpan && valueSpan.classList.contains('range-value')) {
            // 初始化显示值
            valueSpan.textContent = input.value;

            // 监听变化
            input.addEventListener('input', function() {
                valueSpan.textContent = this.value;
            });
        }
    });
}

// 初始化表单提交事件
function initializeFormSubmissions() {
    // DeepSeek API配置表单
    const deepseekApiForm = document.getElementById('deepseek-api-form');
    if (deepseekApiForm) {
        deepseekApiForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDeepSeekApiConfig();
        });
    }

    // DeepSeek模型配置表单
    const deepseekModelForm = document.getElementById('deepseek-model-form');
    if (deepseekModelForm) {
        deepseekModelForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDeepSeekModelConfig();
        });
    }

    // 豆包API配置表单
    const doubaoApiForm = document.getElementById('doubao-api-form');
    if (doubaoApiForm) {
        doubaoApiForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDoubaoApiConfig();
        });
    }

    // 豆包模型配置表单
    const doubaoModelForm = document.getElementById('doubao-model-form');
    if (doubaoModelForm) {
        doubaoModelForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDoubaoModelConfig();
        });
    }
}

// 密码显示/隐藏切换
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

// 保存DeepSeek API配置
function saveDeepSeekApiConfig() {
    const formData = new FormData(document.getElementById('deepseek-api-form'));
    const config = Object.fromEntries(formData);

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'save_deepseek_api_config',
            config: config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('DeepSeek API配置保存成功', 'success');
        } else {
            showNotification('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('保存失败: 网络错误', 'error');
    });
}

// 保存DeepSeek模型配置
function saveDeepSeekModelConfig() {
    const formData = new FormData(document.getElementById('deepseek-model-form'));
    const config = Object.fromEntries(formData);

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'save_deepseek_model_config',
            config: config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('DeepSeek模型配置保存成功', 'success');
        } else {
            showNotification('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('保存失败: 网络错误', 'error');
    });
}

// 保存豆包API配置
function saveDoubaoApiConfig() {
    const formData = new FormData(document.getElementById('doubao-api-form'));
    const config = Object.fromEntries(formData);

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'save_doubao_api_config',
            config: config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('豆包API配置保存成功', 'success');
        } else {
            showNotification('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('保存失败: 网络错误', 'error');
    });
}

// 保存豆包模型配置
function saveDoubaoModelConfig() {
    const formData = new FormData(document.getElementById('doubao-model-form'));
    const config = Object.fromEntries(formData);

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'save_doubao_model_config',
            config: config
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('豆包模型配置保存成功', 'success');
        } else {
            showNotification('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('保存失败: 网络错误', 'error');
    });
}

// 测试DeepSeek连接
function testDeepSeekConnection() {
    const apiKey = document.getElementById('deepseek_api_key').value;
    const baseUrl = document.getElementById('deepseek_base_url').value;

    if (!apiKey) {
        showNotification('请先输入API密钥', 'warning');
        return;
    }

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'test_deepseek_connection',
            api_key: apiKey,
            base_url: baseUrl
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('DeepSeek连接测试成功', 'success');
        } else {
            showNotification('连接测试失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('连接测试失败: 网络错误', 'error');
    });
}

// 测试豆包连接
function testDoubaoConnection() {
    const apiKey = document.getElementById('doubao_api_key').value;
    const baseUrl = document.getElementById('doubao_base_url').value;
    const endpointId = document.getElementById('doubao_endpoint_id').value;

    if (!apiKey || !endpointId) {
        showNotification('请先输入API密钥和端点ID', 'warning');
        return;
    }

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'test_doubao_connection',
            api_key: apiKey,
            base_url: baseUrl,
            endpoint_id: endpointId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('豆包连接测试成功', 'success');
        } else {
            showNotification('连接测试失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('连接测试失败: 网络错误', 'error');
    });
}

// 测试DeepSeek API
function testDeepSeekAPI() {
    const message = document.getElementById('deepseek_test_message').value;
    if (!message.trim()) {
        showNotification('请输入测试消息', 'warning');
        return;
    }

    const resultDiv = document.getElementById('deepseek_test_result');
    const contentDiv = resultDiv.querySelector('.result-content');

    resultDiv.style.display = 'block';
    contentDiv.textContent = '正在发送请求...';

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'test_deepseek_api',
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            contentDiv.textContent = data.response;
            showNotification('DeepSeek API测试成功', 'success');
        } else {
            contentDiv.textContent = '错误: ' + data.message;
            showNotification('API测试失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        contentDiv.textContent = '网络错误: ' + error.message;
        showNotification('API测试失败: 网络错误', 'error');
    });
}

// 测试豆包API
function testDoubaoAPI() {
    const message = document.getElementById('doubao_test_message').value;
    if (!message.trim()) {
        showNotification('请输入测试消息', 'warning');
        return;
    }

    const resultDiv = document.getElementById('doubao_test_result');
    const contentDiv = resultDiv.querySelector('.result-content');

    resultDiv.style.display = 'block';
    contentDiv.textContent = '正在发送请求...';

    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'test_doubao_api',
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            contentDiv.textContent = data.response;
            showNotification('豆包API测试成功', 'success');
        } else {
            contentDiv.textContent = '错误: ' + data.message;
            showNotification('API测试失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        contentDiv.textContent = '网络错误: ' + error.message;
        showNotification('API测试失败: 网络错误', 'error');
    });
}

// 加载可用模型
function loadAvailableModels(provider) {
    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_available_models',
            provider: provider
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const selectElement = document.getElementById(provider + '_model');
            // 清空现有选项（保留第一个默认选项）
            while (selectElement.children.length > 1) {
                selectElement.removeChild(selectElement.lastChild);
            }

            // 添加新的模型选项
            data.models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.name;
                selectElement.appendChild(option);
            });

            showNotification(provider + '模型列表已更新', 'success');
        } else {
            showNotification('获取模型列表失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('获取模型列表失败: 网络错误', 'error');
    });
}

// 刷新统计数据
function refreshDeepSeekStats() {
    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_deepseek_stats'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('deepseek_total_requests').textContent = data.stats.total_requests;
            document.getElementById('deepseek_success_rate').textContent = data.stats.success_rate + '%';
            document.getElementById('deepseek_avg_response_time').textContent = data.stats.avg_response_time + 'ms';
            document.getElementById('deepseek_token_usage').textContent = data.stats.token_usage;
            showNotification('DeepSeek统计数据已更新', 'success');
        } else {
            showNotification('获取统计数据失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('获取统计数据失败: 网络错误', 'error');
    });
}

// 刷新豆包统计数据
function refreshDoubaoStats() {
    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_doubao_stats'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('doubao_total_requests').textContent = data.stats.total_requests;
            document.getElementById('doubao_success_rate').textContent = data.stats.success_rate + '%';
            document.getElementById('doubao_avg_response_time').textContent = data.stats.avg_response_time + 'ms';
            document.getElementById('doubao_token_usage').textContent = data.stats.token_usage;
            showNotification('豆包统计数据已更新', 'success');
        } else {
            showNotification('获取统计数据失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('获取统计数据失败: 网络错误', 'error');
    });
}

// 加载保存的配置
function loadSavedConfigurations() {
    // 加载DeepSeek配置
    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_deepseek_config'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.config) {
            populateForm('deepseek', data.config);
        }
    })
    .catch(error => {
        console.error('Error loading DeepSeek config:', error);
    });

    // 加载豆包配置
    fetch('../../api/customer_service_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'get_doubao_config'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.config) {
            populateForm('doubao', data.config);
        }
    })
    .catch(error => {
        console.error('Error loading Doubao config:', error);
    });
}

// 填充表单数据
function populateForm(provider, config) {
    Object.keys(config).forEach(key => {
        const element = document.getElementById(provider + '_' + key);
        if (element) {
            element.value = config[key];

            // 如果是滑块，更新显示值
            if (element.type === 'range') {
                const valueSpan = element.nextElementSibling;
                if (valueSpan && valueSpan.classList.contains('range-value')) {
                    valueSpan.textContent = config[key];
                }
            }
        }
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// 获取通知图标
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}
</script>

<!-- 通知样式 -->
<style>
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10000;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

.notification-close {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    margin-left: auto;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
