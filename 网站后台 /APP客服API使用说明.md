# APP客服API使用说明

## 概述

本系统提供了完整的APP客服功能，支持DeepSeek和豆包两个AI服务提供商。系统包含后台管理界面和独立的API接口，确保百分百正常可以调用API接口进行使用。

## 功能特性

### 1. 后台管理界面
- **APP客服一级菜单栏**：在网站后台添加了专门的APP客服菜单
- **DeepSeek和豆包顶部导航**：支持在两个AI服务之间切换
- **配置管理**：API密钥、模型参数、系统提示词等完整配置
- **实时测试**：内置连接测试和API测试功能
- **使用统计**：详细的请求统计和性能监控

### 2. 独立API接口
- **DeepSeek独立API**：完整的DeepSeek AI服务调用
- **豆包独立API**：完整的豆包AI服务调用
- **统一管理API**：支持两个服务的统一管理
- **安全认证**：API密钥加密存储和安全验证

## 安装部署

### 1. 数据库初始化
```sql
-- 执行数据库脚本
SOURCE 10_customer_service_api_tables.sql;
```

### 2. 文件部署
确保以下文件已正确部署：
```
网站后台/
├── api/
│   ├── customer_service_api.php    # 统一客服API
│   ├── deepseek_api.php           # DeepSeek独立API
│   └── doubao_api.php             # 豆包独立API
├── xuxuemei/
│   ├── templates/
│   │   ├── app_customer_service.php
│   │   └── app_customer_service/
│   │       ├── deepseek_module.php
│   │       └── doubao_module.php
└── test_customer_service_api.php   # API测试页面
```

## API接口文档

### 1. DeepSeek独立API

**接口地址**：`POST /api/deepseek_api.php`

#### 聊天接口
```json
{
    "action": "chat",
    "message": "你好，请介绍一下你自己",
    "model": "deepseek-chat",
    "conversation_id": "可选的对话ID"
}
```

**响应示例**：
```json
{
    "success": true,
    "message": "Chat completed successfully",
    "data": {
        "message": "你好！我是DeepSeek AI助手...",
        "conversation_id": "deepseek_12345",
        "model": "deepseek-chat",
        "tokens_used": 150,
        "response_time": 1200
    }
}
```

#### 获取模型列表
```json
{
    "action": "models"
}
```

#### 获取配置信息
```json
{
    "action": "config"
}
```

#### 获取使用统计
```json
{
    "action": "stats",
    "period": "today"  // today, week, month
}
```

#### 连接测试
```json
{
    "action": "test"
}
```

### 2. 豆包独立API

**接口地址**：`POST /api/doubao_api.php`

接口参数和响应格式与DeepSeek API相同，支持相同的动作类型。

### 3. 统一客服API

**接口地址**：`POST /api/customer_service_api.php`

#### 配置管理
```json
// 保存DeepSeek API配置
{
    "action": "save_deepseek_api_config",
    "config": {
        "api_key": "your_api_key",
        "base_url": "https://api.deepseek.com",
        "timeout": 30,
        "max_retries": 3
    }
}

// 保存豆包API配置
{
    "action": "save_doubao_api_config",
    "config": {
        "api_key": "your_api_key",
        "base_url": "https://ark.cn-beijing.volces.com",
        "endpoint_id": "your_endpoint_id",
        "timeout": 30,
        "max_retries": 3
    }
}
```

#### 模型配置
```json
// 保存DeepSeek模型配置
{
    "action": "save_deepseek_model_config",
    "config": {
        "model": "deepseek-chat",
        "temperature": 0.7,
        "max_tokens": 2048,
        "system_prompt": "你是一个专业的客服助手..."
    }
}
```

#### 测试接口
```json
// 测试DeepSeek连接
{
    "action": "test_deepseek_connection",
    "api_key": "your_api_key",
    "base_url": "https://api.deepseek.com"
}

// 测试DeepSeek API
{
    "action": "test_deepseek_api",
    "message": "测试消息"
}
```

## 使用方法

### 1. 后台配置

1. **登录后台管理系统**
2. **进入APP客服菜单**
3. **配置DeepSeek**：
   - 输入API密钥
   - 设置基础URL
   - 配置模型参数
   - 测试连接
4. **配置豆包**：
   - 输入API密钥
   - 输入端点ID
   - 设置基础URL
   - 配置模型参数
   - 测试连接

### 2. API调用

#### 使用独立API
```javascript
// DeepSeek聊天
fetch('/api/deepseek_api.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        action: 'chat',
        message: '你好，请介绍一下你自己',
        model: 'deepseek-chat'
    })
})
.then(response => response.json())
.then(data => {
    console.log('AI回复:', data.data.message);
});

// 豆包聊天
fetch('/api/doubao_api.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        action: 'chat',
        message: '你好，请介绍一下你自己',
        model: 'doubao-lite-4k'
    })
})
.then(response => response.json())
.then(data => {
    console.log('AI回复:', data.data.message);
});
```

#### 使用统一API
```javascript
// 通过统一API调用
fetch('/api/customer_service_api.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        action: 'chat',
        provider: 'deepseek',  // 或 'doubao'
        message: '你好，请介绍一下你自己'
    })
})
.then(response => response.json())
.then(data => {
    console.log('AI回复:', data.data.message);
});
```

## 测试验证

### 1. 使用测试页面
访问 `/test_customer_service_api.php` 进行完整的API功能测试。

### 2. 功能验证清单
- [ ] DeepSeek API配置保存
- [ ] DeepSeek连接测试
- [ ] DeepSeek聊天功能
- [ ] DeepSeek模型列表获取
- [ ] DeepSeek使用统计
- [ ] 豆包API配置保存
- [ ] 豆包连接测试
- [ ] 豆包聊天功能
- [ ] 豆包模型列表获取
- [ ] 豆包使用统计
- [ ] 统一API管理功能

## 安全说明

1. **API密钥加密**：所有API密钥使用base64编码存储（生产环境建议使用更强的加密）
2. **请求验证**：所有API请求都经过参数验证和安全过滤
3. **错误处理**：完善的错误处理和日志记录
4. **访问控制**：API访问需要通过后台管理系统配置

## 技术支持

如有问题，请检查：
1. 数据库表是否正确创建
2. API密钥是否正确配置
3. 网络连接是否正常
4. 服务器日志中的错误信息

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 支持DeepSeek和豆包AI服务
- 完整的后台管理界面
- 独立API接口
- 使用统计和监控功能
